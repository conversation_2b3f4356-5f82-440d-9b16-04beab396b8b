part of xr_helper;

class NetworkApiServices extends BaseApiServices {
  static const _timeOutDuration = Duration(seconds: 30);

  // * Get request ================================
  @override
  Future<dynamic> getResponse(String url, {bool blobData = false}) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.i('GetApiUrl => $apiUrl');

      if (blobData) {
        final response = await http.readBytes(
          // Uri.parse(ApiStrings.apiUrl(sl<ConfigData>().subDomain!) + url),
          apiUrl,
          headers: headers,
        );

        responseJson = response;
      } else {
        final response =
            await http.get(apiUrl, headers: headers).timeout(_timeOutDuration);

        Log.f(
            'GetRes $apiUrl => ${response.body}\nStatus Code => ${response.statusCode}');

        responseJson = returnResponse(response);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
    return responseJson;
  }

  // * Post request ================================
  @override
  Future postResponse(
    String url, {
    required Map<String, dynamic> body,
    List<String> filePaths = const [],
    bool fromAuth = false,
  }) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.w(
          'PostApiUrl => $apiUrl\n 💾💾💾 PostData -> $body 💾💾💾 & filePaths => $filePaths');

      late http.Response response;

      response = await _postOrPutAndUploadFiles(
        apiUrl,
        body: body,
        filePaths: filePaths,
        method: 'POST',
        fromAuth: fromAuth,
      );

      Log.f(
          'PostRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = await jsonDecode(response.body);

        _handleData(responseJson);
        return responseJson;
      } else {
        responseJson = await jsonDecode(response.body);

        _handleData(responseJson, isErrorMessage: true);

        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
  }

  // * Put request ================================
  @override
  Future putResponse(
    String url, {
    required Map<String, dynamic> data,
    List<String> filePaths = const [],
    bool fromAuth = false,
  }) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.w(
          'PutApiUrl => $apiUrl\n 💾💾💾 PutData -> $data 💾💾💾 & filePaths => $filePaths');

      late http.Response response;

      if (filePaths.isNotEmpty) {
        response = await _postOrPutAndUploadFiles(
          apiUrl,
          body: data,
          filePaths: filePaths,
          method: 'PUT',
          fromAuth: fromAuth,
        );
      } else {
        response = await http
            .put(apiUrl, body: jsonEncode(data), headers: headers)
            .timeout(_timeOutDuration);
      }

      Log.f(
          'PutRes => ${response.body}\nStatus Code => ${response.statusCode}');

      responseJson = await jsonDecode(response.body);

      final isContainsError = responseJson.containsKey('error');

      if ((response.statusCode == 200 || response.statusCode == 201) &&
          !isContainsError) {
        _handleData(responseJson);
      } else {
        _handleData(responseJson, isErrorMessage: true);
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
    return responseJson;
  }

  void _handleData(
    responseJson, {
    bool isErrorMessage = true,
  }) {
    final msg = responseJson.containsKey('message')
        ? responseJson['message'].toString()
        : '';

    if (msg.isNotEmpty) {
      Log.i('Message => $msg');
      showToast(msg, isError: isErrorMessage);
    }

    final accessToken =
        responseJson.containsKey('jwt') ? responseJson['jwt'].toString() : '';

    if (accessToken.isNotEmpty) {
      GetStorageService.setData(key: LocalKeys.token, value: accessToken);
    }

    final errors = responseJson.containsKey('errors')
        ? responseJson['errors'] as Map<String, dynamic>
        : {};

    if (errors.isNotEmpty) {
      final error = errors.values.first.first.toString();
      Log.e('Error => $error');
      showToast(error, isError: true);
    }

    if (responseJson.containsKey('error')) {
      final error = responseJson['error'];
      final errorMessage = error.containsKey('message')
          ? error['message'].toString()
          : 'An error occurred';
      Log.e('Error => $errorMessage');
      showToast(errorMessage, isError: true);
    }
  }

  // void _handleData(
  //   responseJson, {
  //   bool isErrorMessage = true,
  // }) {
  //   final msg = responseJson.containsKey('message')
  //       ? responseJson['message'].toString()
  //       : '';
  //
  //   if (msg.isNotEmpty) {
  //     Log.i('Message => $msg');
  //
  //     showToast(msg, isError: isErrorMessage);
  //   }
  //
  //   final accessToken = responseJson.containsKey('jwt')
  //       ? responseJson['jwt'].toString()
  //       : '';
  //
  //   if (accessToken.isNotEmpty) {
  //     GetStorageService.setData(key: LocalKeys.token, value: accessToken);
  //   }
  //
  //   final errors = responseJson.containsKey('errors')
  //       ? responseJson['errors'] as Map<String, dynamic>
  //       : {};
  //
  //   if (errors.isNotEmpty) {
  //     final error = errors.values.first.first.toString();
  //     Log.e('Error => $error');
  //     showToast(error, isError: true);
  //   }
  // }

  // * Post Or Put & Upload File request ================================
  Future _postOrPutAndUploadFiles(
    Uri url, {
    required Map<String, dynamic> body,
    required List<String> filePaths,
    required String method,
    bool fromAuth = false,
  }) async {
    late http.Response response;

    //? Add headers
    // request.headers.addAll(headers);

    //? Add files
    // for (var image in filePaths) {
    //   request.files.add(await http.MultipartFile.fromPath('files[]', image));
    // }

    // final Map<String, String> fieldsData = body.map((key, value) {
    //   return MapEntry(key, value.toString());
    // });

    final Map<String, String> fieldsData;
    if (fromAuth) {
      http.MultipartRequest request = http.MultipartRequest(method, url);

      fieldsData = request.fields
        ..addAll(
          body.map((key, value) => MapEntry(key, value.toString())),
        );

      request.fields.addAll(fieldsData);
      response = await http.Response.fromStream(await request.send())
          .timeout(_timeOutDuration);
    } else {
      response = await http.post(
        url,
        body: jsonEncode({
          "data": body,
        }),
        headers: headers,
      );

      // final encodedData = jsonEncode(body);
      //
      // fieldsData = request.fields
      //   ..addAll({
      //     "data": encodedData,
      //   });
    }

    Log.f('PostRes => ${response.body}\nStatus_Code => ${response.statusCode}');

    return response;
  }

  // * Delete request ================================
  @override
  Future deleteResponse(String url) async {
    try {
      final apiUrl = Uri.parse(url);

      Log.i('DeleteApiUrl => $apiUrl');

      final response =
          await http.delete(apiUrl, headers: headers).timeout(_timeOutDuration);

      Log.f(
          'DeleteRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
  }
}
