part of xr_helper;

extension StringExtenstions on String? {
  //! Date Time ================================
  String get formatStringToDate {
    if (this == null || this!.isEmpty) return '';
    return DateFormat('yyyy-MM-dd').format(DateTime.parse(this!));
  }
}

extension URLLauncher on String {
  Future<void> launchURL({
    bool isPhone = false,
    bool isWhatsapp = false,
  }) async {
    await launchUrl(Uri.parse(
      isPhone
          ? 'tel:$this'
          : isWhatsapp
              ? 'https://wa.me/$this'
              : this,
    ));

    // if (await canLaunchUrl(Uri.parse(this))) {
    //   await launchUrl(Uri.parse(this));
    // } else {
    //   throw 'Could not launch $this';
    // }
  }

  //? Call Phone Number
  Future<void> call() async {
    if (await canLaunchUrl(Uri.parse('tel:$this'))) {
      await launchUrl(Uri.parse('tel:$this'));
    } else {
      throw 'Could not launch $this';
    }
  }
}

extension ImageExtensions on String? {
  //? Image Network
  Widget networkImage({
    double? height,
    double? width,
    BoxFit fit = BoxFit.cover,
    double? radius,
  }) {
    return BaseCachedImage(
      this ?? '',
      height: height,
      width: width,
      fit: fit,
      radius: radius,
      showErrorIcon: this == null || this!.isNotEmpty,
    ).clipRRect(radius: radius);
  }

  //? Image Asset
  Widget assetImage({
    double? height,
    double? width,
    BoxFit? fit,
    double? radius,
    Color? color,
  }) {
    return Image.asset(
      this ?? '',
      height: height,
      width: width,
      fit: fit,
      color: color,
    ).clipRRect(radius: radius);
  }

  //? Image File
  Widget fileImage({
    double? height,
    double? width,
    BoxFit? fit,
    double? radius,
  }) {
    return Image.file(
      File(this ?? ''),
      height: height,
      width: width,
      fit: fit,
    ).clipRRect(radius: radius);
  }
}
