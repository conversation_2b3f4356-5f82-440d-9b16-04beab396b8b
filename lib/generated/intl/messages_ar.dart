// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("من نحن"),
    "appStore": MessageLookupByLibrary.simpleMessage("متجر آبل"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "callUs": MessageLookupByLibrary.simpleMessage("☎️ اتصل بنا"),
    "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
    "contactUs": MessageLookupByLibrary.simpleMessage("اتصل بنا"),
    "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
    "facebook": MessageLookupByLibrary.simpleMessage("فيسبوك"),
    "followUs": MessageLookupByLibrary.simpleMessage("💫 تابعنا"),
    "instagram": MessageLookupByLibrary.simpleMessage("إنستغرام"),
    "linkedin": MessageLookupByLibrary.simpleMessage("لينكدإن"),
    "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "playStore": MessageLookupByLibrary.simpleMessage("متجر جوجل"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "selectLanguage": MessageLookupByLibrary.simpleMessage("اختر اللغة"),
    "sizes": MessageLookupByLibrary.simpleMessage("المقاسات"),
    "social": MessageLookupByLibrary.simpleMessage("التواصل الاجتماعي"),
    "stayUpdatedOnNewProductsAndOrderNow": MessageLookupByLibrary.simpleMessage(
      "تابع آخر المنتجات واطلب الآن",
    ),
    "textUs": MessageLookupByLibrary.simpleMessage("💬 أرسل لنا رسالة"),
    "tiktok": MessageLookupByLibrary.simpleMessage("تيك توك"),
    "twitter": MessageLookupByLibrary.simpleMessage("تويتر"),
    "website": MessageLookupByLibrary.simpleMessage("الموقع الإلكتروني"),
    "whatsapp": MessageLookupByLibrary.simpleMessage("واتساب"),
    "youtube": MessageLookupByLibrary.simpleMessage("يوتيوب"),
  };
}
