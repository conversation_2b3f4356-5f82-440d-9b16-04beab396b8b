import 'package:flutter/material.dart';
import 'package:idea2app_qr_landing/main.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/core/theme/color_manager.dart';
import 'package:quickalert/quickalert.dart';
import 'package:xr_helper/xr_helper.dart';

class FooterSection extends StatelessWidget {
  const FooterSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (currentVendor?.aboutVendor?.privacy != null &&
                currentVendor?.aboutVendor?.privacy != "") ...[
              InkWell(
                onTap: () {
                  QuickAlert.show(
                      context: context,
                      type: QuickAlertType.info,
                      confirmBtnColor: ColorManager.primaryColor,
                      confirmBtnText: context.tr.close,
                      title: context.tr.privacyPolicy,
                      text: currentVendor?.aboutVendor?.privacy);
                },
                child: Text(
                  context.tr.privacyPolicy,
                  style: TextStyle(
                    fontSize: context.isDesktop ? 20 : 16,
                    color: ColorManager.darkGrey,
                  ),
                ),
              ),
              AppGaps.gap12,
            ],
            if (currentVendor?.aboutVendor?.about != null &&
                currentVendor?.aboutVendor?.about != "")
              InkWell(
                onTap: () {
                  QuickAlert.show(
                      context: context,
                      type: QuickAlertType.info,
                      confirmBtnColor: ColorManager.primaryColor,
                      confirmBtnText: context.tr.close,
                      title: context.tr.aboutUs,
                      text: currentVendor?.aboutVendor?.about);
                },
                child: Text(
                  context.tr.aboutUs,
                  style: TextStyle(
                    fontSize: context.isDesktop ? 20 : 16,
                    color: ColorManager.darkGrey,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
