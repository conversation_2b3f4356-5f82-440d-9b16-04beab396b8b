import 'package:flutter/material.dart';
import 'package:idea2app_qr_landing/generated/assets.gen.dart';
import 'package:idea2app_qr_landing/main.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/core/shared/widgets/container/base_container.widget.dart';
import 'package:idea2app_qr_landing/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class SocialMediaSection extends StatelessWidget {
  const SocialMediaSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final socialMedia = currentVendor?.aboutVendor;

    final socials = [
      //! Phone ---------------------------
      ContactUsColumn(
        titleAndSubTitleAndIconPathAndLink: (
          context.tr.phone,
          context.tr.callUs,
          Assets.icons.phone.path,
          currentVendor?.phone ?? '',
        ),
      ),

      AppGaps.gap16,

      //! WhatsApp ---------------------------
      ContactUsColumn(
        titleAndSubTitleAndIconPathAndLink: (
          context.tr.whatsapp,
          context.tr.textUs,
          Assets.icons.whatsapp.path,
          socialMedia?.whatsapp ?? '',
        ),
      ),

      AppGaps.gap16,

      //! Facebook ---------------------------
      ContactUsColumn(
        titleAndSubTitleAndIconPathAndLink: (
          context.tr.facebook,
          context.tr.followUs,
          Assets.icons.facebook.path,
          socialMedia?.facebook ?? '',
        ),
        // color: ColorManager.facebookColor,
      ),

      AppGaps.gap16,

      //! Instagram ---------------------------
      ContactUsColumn(
        titleAndSubTitleAndIconPathAndLink: (
          context.tr.instagram,
          context.tr.followUs,
          Assets.icons.instagram.path,
          socialMedia?.instagram ?? '',
        ),
      ),

      AppGaps.gap16,

      //! Tiktok ---------------------------
      ContactUsColumn(
        titleAndSubTitleAndIconPathAndLink: (
          context.tr.tiktok,
          context.tr.followUs,
          Assets.icons.tiktok.path,
          socialMedia?.tiktok ?? '',
        ),
      ),

      AppGaps.gap16,

      //! youtube ---------------------------
      ContactUsColumn(
        titleAndSubTitleAndIconPathAndLink: (
          context.tr.youtube,
          context.tr.subscribeToOurChannel,
          Assets.icons.youtube.path,
          socialMedia?.youtube ?? '',
        ),
      ),

      //! Website ---------------------------
      // ContactUsColumn(
      //   titleAndSubTitleAndIconPAthAndLink: (
      //     context.tr.website,
      //     Assets.iconsWebsite,
      //     contactUS?.website?.url ?? '',
      //   ),
      // ),

      //! Email ---------------------------
      // ContactUsColumn(
      //   titleAndSubTitleAndIconPAthAndLink: (
      //   context.tr.email,
      //   Assets.icons.email,
      //   contactUS?.email?.url ?? '',
      //   ),
      // ),
    ];

    return SizedBox(
      width: context.isDesktop ? context.width * 0.4 : context.width,
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        child: Column(
          children: socials,
        ),
      ),
    );
  }
}

class ContactUsColumn extends StatelessWidget {
  final (
    String title,
    String subTitle,
    String iconPath,
    String link
  ) titleAndSubTitleAndIconPathAndLink;

  const ContactUsColumn({
    required this.titleAndSubTitleAndIconPathAndLink,
  });

  @override
  Widget build(BuildContext context) {
    final title = titleAndSubTitleAndIconPathAndLink.$1;
    final subTitle = titleAndSubTitleAndIconPathAndLink.$2;
    final iconPath = titleAndSubTitleAndIconPathAndLink.$3;
    final link = titleAndSubTitleAndIconPathAndLink.$4;

    if (link.isEmpty) return const SizedBox.shrink();

    final isPhone = title == context.tr.phone;
    final isWhatsapp = title == context.tr.whatsapp;

    return BaseContainer(
      padding: const EdgeInsets.symmetric(
        vertical: AppSpaces.padding8,
        horizontal: AppSpaces.padding12,
      ),
      shadow: [
        BoxShadow(
          color: ColorManager.primaryColor.withOpacity(0.1),
          blurRadius: 10,
          spreadRadius: 3,
          offset: const Offset(0, 2),
        ),
      ],
      child: ListTile(
          contentPadding:
              const EdgeInsets.symmetric(horizontal: AppSpaces.padding4),
          onTap: () => link.launchURL(isPhone: isPhone, isWhatsapp: isWhatsapp),
          trailing: Icon(
            Icons.arrow_forward_ios_rounded,
            size: context.isDesktop ? 20 : 15,
            color: ColorManager.primaryColor,
          ),
          leading: Image.asset(
            iconPath,
            width: context.isDesktop ? 40 : 34,
            height: context.isDesktop ? 40 : 34,
            fit: BoxFit.contain,
            color: isPhone ? ColorManager.primaryColor : null,
          ),
          title: Text(
            title,
            style: AppTextStyles.title,
          ),
          subtitle: Text(
            subTitle,
            style: AppTextStyles.hint.copyWith(
              fontSize: 14,
              color: ColorManager.darkGrey,
              fontWeight: FontWeight.bold,
            ),
          )),
    );
  }
}
