import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/num_extensions.dart';
import 'package:idea2app_qr_landing/src/screens/menu/models/products_model.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:xr_helper/xr_helper.dart' show AppGaps, AppRadius, AppSpaces;

import '../../../core/theme/color_manager.dart';

class ProductDetailsScreen extends HookWidget {
  final ProductModel product;

  const ProductDetailsScreen({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final controller = usePageController(initialPage: 0);
    final images = product.images ?? [];

    final isSale = product.isSale;

    final salePrice = product.salePrice?.toCurrency(context);

    final productName = context.isEnglish
        ? (product.englishTitle?.isNotEmpty == true
            ? product.englishTitle!
            : product.arabicTitle ?? '')
        : (product.arabicTitle?.isNotEmpty == true
            ? product.arabicTitle!
            : product.englishTitle ?? '');

    final productDescription = context.isEnglish
        ? (product.englishDescription?.isNotEmpty == true
            ? product.englishDescription!
            : product.arabicDescription ?? '')
        : (product.arabicDescription?.isNotEmpty == true
            ? product.arabicDescription!
            : product.englishDescription ?? '');

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppSpaces.padding12),
        children: [
          Stack(
            alignment: Alignment.bottomCenter,
            children: [
              SizedBox(
                height: 280.h,
                child: PageView.builder(
                  controller: controller,
                  itemCount: images.length,
                  itemBuilder: (context, index) {
                    final imageUrl = images[index].url ?? '';
                    final heroTag = 'product_image_$index';

                    return GestureDetector(
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => FullScreenImageViewer(
                              imageUrl: imageUrl,
                              heroTag: heroTag,
                            ),
                          ),
                        );
                      },
                      child: Hero(
                        tag: heroTag,
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius16),
                          child: Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Center(
                                child: Icon(Icons.image),
                              );
                            },
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.padding8,
                    vertical: AppSpaces.padding4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppRadius.radius20),
                ),
                margin: const EdgeInsets.only(bottom: AppSpaces.padding20),
                child: SmoothPageIndicator(
                  controller: controller,
                  count: images.length,
                  axisDirection: Axis.horizontal,
                  effect: ExpandingDotsEffect(
                    dotColor: Colors.grey,
                    dotWidth: 7,
                    dotHeight: 5.h,
                    activeDotColor: ColorManager.primaryColor,
                  ),
                ),
              ),
            ],
          ),
          AppGaps.gap16,
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      productName,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if (isSale) ...[
                        Text(
                          salePrice ?? '',
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: ColorManager.primaryColor),
                        ),
                        AppGaps.gap4,
                      ],
                      Text(
                        product.totalPrice?.toCurrency(context) ?? '',
                        style: isSale
                            ? TextStyle(
                                fontWeight: FontWeight.normal,
                                decoration: TextDecoration.lineThrough,
                                decorationColor:
                                    ColorManager.darkGrey.withOpacity(0.5),
                              )
                            : TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: ColorManager.primaryColor),
                      ),
                    ],
                  )
                ],
              ),
              AppGaps.gap8,
              Text(
                productDescription,
                style: TextStyle(
                  fontSize: 15,
                  color: Colors.black.withOpacity(0.5),
                ),
              ),
            ],
          ),
          AppGaps.gap16,
          if (product.sizes.isNotEmpty) ...[
            Text(
              context.tr.sizes,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap4,
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: product.sizes
                  .map((size) => Chip(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: const BorderSide(
                            color: ColorManager.grey,
                          ),
                        ),
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(size.englishName ?? ''),
                            if (size.price != null)
                              Text(': ${size.price?.toCurrency(context) ?? ''}')
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ]
        ],
      ),
    );
  }
}

class FullScreenImageViewer extends StatelessWidget {
  final String imageUrl;
  final String heroTag;

  const FullScreenImageViewer({
    Key? key,
    required this.imageUrl,
    required this.heroTag,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Center(
            child: Hero(
              tag: heroTag,
              child: InteractiveViewer(
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Icon(Icons.image, color: Colors.white),
                    );
                  },
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: CircleAvatar(
              backgroundColor: Colors.black.withOpacity(.5),
              child: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.close, color: Colors.white)),
            ),
          )
        ],
      ),
    );
  }
}
