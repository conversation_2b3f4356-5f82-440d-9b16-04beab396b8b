import 'package:flutter/material.dart';
import 'package:idea2app_qr_landing/generated/assets.gen.dart';
import 'package:idea2app_qr_landing/main.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/screens/social/view/widgets/social_media_section.dart';
import 'package:xr_helper/xr_helper.dart';

class MenuAppBarWidget extends StatelessWidget {
  const MenuAppBarWidget({super.key});

  bool _hasAnySocialLinks() {
    final socialMedia = currentVendor?.aboutVendor;

    return (currentVendor?.phone != null && currentVendor?.phone != '') ||
        (socialMedia?.whatsapp != null && socialMedia?.whatsapp != '') ||
        (socialMedia?.facebook != null && socialMedia?.facebook != '') ||
        (socialMedia?.instagram != null && socialMedia?.instagram != '') ||
        (socialMedia?.tiktok != null && socialMedia?.tiktok != '') ||
        (socialMedia?.youtube != null && socialMedia?.youtube != '');
  }

  @override
  Widget build(BuildContext context) {
    final playStoreLink = currentVendor?.playStoreLink;
    final appStoreLink = currentVendor?.appStoreLink;

    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: BaseCachedImage(
              currentVendor?.logoUrl ?? '',
              height: 80,
              fit: BoxFit.contain,
              alignment: context.isEnglish
                  ? Alignment.centerLeft
                  : Alignment.centerRight,
            ),
          ),
          if (playStoreLink != null ||
              appStoreLink != null ||
              _hasAnySocialLinks()) ...[
            Row(
              spacing: 12,
              children: [
                if (playStoreLink != null)
                  SocialCircleButton(
                    iconPath: Assets.icons.google.path,
                    title: context.tr.playStore,
                    link: playStoreLink,
                  ),
                if (appStoreLink != null)
                  SocialCircleButton(
                    iconPath: Assets.icons.apple.path,
                    title: context.tr.appStore,
                    link: appStoreLink,
                  ),
                if (_hasAnySocialLinks())
                  SocialCircleButton(
                    isSocial: true,
                    iconPath: Assets.icons.facebook.path,
                    title: context.tr.contactUs,
                    onTap: () => _showSocialLinks(context),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _showSocialLinks(BuildContext context) {
    final socialMedia = currentVendor?.aboutVendor;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppGaps.gap16,
                if (currentVendor?.phone != null && currentVendor?.phone != '')
                  ContactUsColumn(
                    titleAndSubTitleAndIconPathAndLink: (
                      context.tr.phone,
                      context.tr.callUs,
                      Assets.icons.phone.path,
                      currentVendor?.phone ?? '',
                    ),
                  ),
                if (currentVendor?.phone != null && currentVendor?.phone != '')
                  AppGaps.gap16,
                if (socialMedia?.whatsapp != null &&
                    socialMedia?.whatsapp != '')
                  ContactUsColumn(
                    titleAndSubTitleAndIconPathAndLink: (
                      context.tr.whatsapp,
                      context.tr.textUs,
                      Assets.icons.whatsapp.path,
                      socialMedia?.whatsapp ?? '',
                    ),
                  ),
                if (socialMedia?.whatsapp != null &&
                    socialMedia?.whatsapp != '')
                  AppGaps.gap16,
                if (socialMedia?.facebook != null &&
                    socialMedia?.facebook != '')
                  ContactUsColumn(
                    titleAndSubTitleAndIconPathAndLink: (
                      context.tr.facebook,
                      context.tr.followUs,
                      Assets.icons.facebook.path,
                      socialMedia?.facebook ?? '',
                    ),
                  ),
                if (socialMedia?.facebook != null &&
                    socialMedia?.facebook != '')
                  AppGaps.gap16,
                if (socialMedia?.instagram != null &&
                    socialMedia?.instagram != '')
                  ContactUsColumn(
                    titleAndSubTitleAndIconPathAndLink: (
                      context.tr.instagram,
                      context.tr.followUs,
                      Assets.icons.instagram.path,
                      socialMedia?.instagram ?? '',
                    ),
                  ),
                if (socialMedia?.instagram != null &&
                    socialMedia?.instagram != '')
                  AppGaps.gap16,
                if (socialMedia?.tiktok != null && socialMedia?.tiktok != '')
                  ContactUsColumn(
                    titleAndSubTitleAndIconPathAndLink: (
                      context.tr.tiktok,
                      context.tr.followUs,
                      Assets.icons.tiktok.path,
                      socialMedia?.tiktok ?? '',
                    ),
                  ),
                if (socialMedia?.tiktok != null && socialMedia?.tiktok != '')
                  AppGaps.gap16,
                if (socialMedia?.youtube != null && socialMedia?.youtube != '')
                  ContactUsColumn(
                    titleAndSubTitleAndIconPathAndLink: (
                      context.tr.youtube,
                      context.tr.subscribeToOurChannel,
                      Assets.icons.youtube.path,
                      socialMedia?.youtube ?? '',
                    ),
                  ),
                AppGaps.gap16,
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(context.tr.close),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SocialCircleButton extends StatelessWidget {
  final String iconPath;
  final String title;
  final String? link;
  final VoidCallback? onTap;
  final bool isSocial;

  const SocialCircleButton({
    super.key,
    required this.iconPath,
    required this.title,
    this.link,
    this.onTap,
    this.isSocial = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: onTap ?? (link != null ? () => link!.launchURL() : null),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[100],
            ),
            child: Center(
              child: !isSocial
                  ? Image.asset(
                      iconPath,
                      width: 24,
                      height: 24,
                    )
                  : const Icon(
                      Icons.link,
                      size: 24,
                      color: Colors.black54,
                    ),
            ),
          ),
        ),
        AppGaps.gap8,
        Text(
          title,
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
