import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/screens/menu/models/categories_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';

class CategoriesTabBarWidget extends HookWidget {
  final List<CategoryModel> categories;
  final ValueNotifier<int> tabBarIndex; 
  final Function(int index) onTap;

  const CategoriesTabBarWidget({
    super.key,
    required this.categories, 
    required this.onTap,
    required this.tabBarIndex, 
  });

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: categories.length,
        initialIndex: tabBarIndex.value,
        child: TabBar(
            onTap: (index) {
              tabBarIndex.value = index;
              onTap(index);
            },
            indicatorSize: TabBarIndicatorSize.tab,
            indicatorColor: ColorManager.primaryColor,
            dividerColor: Colors.grey.withOpacity(0.2),
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            tabs: categories.indexed.map(
              (e) {
                final index = e.$1;
                final category = e.$2;

                final categoryName = context.isEnglish
                    ? (category.englishName?.isNotEmpty == true
                        ? category.englishName!
                        : category.arabicName ?? '')
                    : (category.arabicName?.isNotEmpty == true
                        ? category.arabicName!
                        : category.englishName ?? '');

                final isSelected = tabBarIndex.value == index;
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 700),
                        padding: const EdgeInsets.all(5),
                        curve: Curves.easeInOut,
                        height: 55,
                        width: 55,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected
                                ? ColorManager.primaryColor
                                : Colors.transparent,
                            width: 1,
                          ),
                        ),
                        child: BaseCachedImage(
                          category.featureImage?.url ?? '',
                          height: 50,
                          width: 50,
                          radius: AppRadius.radius100,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Text(categoryName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          )),
                    ],
                  ),
                );
              },
            ).toList()));
  }
}
