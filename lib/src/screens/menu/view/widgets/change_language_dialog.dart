import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/consts/app_constants.dart' show AppConsts;
import '../../../../core/shared/services/app_settings/controller/settings_controller.dart';
import '../../../../core/theme/color_manager.dart';

class ChangeLanguageDialog extends ConsumerWidget {
  const ChangeLanguageDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(appSettingsControllerProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.selectLanguage,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap12,
            ...AppConsts.supportedLocales.map((loc) {
              final isSelected =
                  settingsController.locale.languageCode == loc.languageCode;

              return InkWell(
                onTap: () {
                  context.back();
                  settingsController.updateLanguage(loc);
                },
                child: Container(
                  margin: const EdgeInsets.only(bottom: AppSpaces.padding4),
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.padding12,
                      vertical: AppSpaces.padding8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? ColorManager.primaryColor
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(AppRadius.radius4),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        AppConsts.langCodeToString(context,
                            langCode: loc.languageCode),
                        style: TextStyle(
                          color: isSelected ? ColorManager.white : null,
                        ),
                      ),
                      Radio<String>(
                        value: loc.languageCode,
                        groupValue: settingsController.locale.languageCode,
                        toggleable: true,
                        activeColor: ColorManager.white,
                        onChanged: (_) {},
                      ),
                    ],
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
