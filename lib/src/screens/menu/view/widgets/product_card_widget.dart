import 'package:flutter/material.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/num_extensions.dart';
import 'package:idea2app_qr_landing/src/screens/menu/models/products_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';
import '../product_details_screen.dart';

class ProductCardWidget extends StatelessWidget {
  final ProductModel product;

  const ProductCardWidget({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final isSale = product.isSale;

    final salePrice = product.salePrice?.toCurrency(context);
    final productName = context.isEnglish
        ? (product.englishTitle?.isNotEmpty == true
            ? product.englishTitle!
            : product.arabicTitle ?? '')
        : (product.arabicTitle?.isNotEmpty == true
            ? product.arabicTitle!
            : product.englishTitle ?? '');

    final productDescription = context.isEnglish
        ? (product.englishDescription?.isNotEmpty == true
            ? product.englishDescription!
            : product.arabicDescription ?? '')
        : (product.arabicDescription?.isNotEmpty == true
            ? product.arabicDescription!
            : product.englishDescription ?? '');

    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailsScreen(product: product),
            ));
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          BaseCachedImage(product.thumbnail?.url ?? '',
              height: 100,
              width: 120,
              radius: AppRadius.radius12,
              fit: BoxFit.cover),
          AppGaps.gap8,
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                productName,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                productDescription,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black.withOpacity(0.5),
                ),
              ),
              //! product title & price & sale price
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //! price and sale price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if (isSale) ...[
                        Text(salePrice ?? '',
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: ColorManager.primaryColor)),
                        AppGaps.gap4,
                      ],
                      Text(
                        product.totalPrice?.toCurrency(context) ?? '',
                        style: isSale
                            ? TextStyle(
                                fontWeight: isSale ? null : FontWeight.bold,
                                decoration:
                                    isSale ? TextDecoration.lineThrough : null,
                                decorationColor: context.isDark
                                    ? ColorManager.grey.withOpacity(0.7)
                                    : ColorManager.darkGrey.withOpacity(0.5),
                              )
                            : TextStyle(
                                fontWeight: isSale ? null : FontWeight.bold,
                                decoration:
                                    isSale ? TextDecoration.lineThrough : null,
                                fontSize: 16,
                                color: ColorManager.primaryColor),
                      ),
                    ],
                  ).sized(
                    width: context.width * 0.5,
                  ),
                ],
              ),
            ],
          )),
        ],
      ),
    );
  }
}
