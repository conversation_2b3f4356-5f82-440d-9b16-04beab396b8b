import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/screens/menu/models/products_model.dart';
import 'package:idea2app_qr_landing/src/screens/menu/view/widgets/categories_tab_bar_widget.dart';
import 'package:idea2app_qr_landing/src/screens/menu/view/widgets/change_language_dialog.dart'
    show ChangeLanguageDialog;
import 'package:idea2app_qr_landing/src/screens/menu/view/widgets/menu_app_bar_widget.dart';
import 'package:idea2app_qr_landing/src/screens/menu/view/widgets/product_card_widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../generated/assets.gen.dart';
import '../../../../main.dart';

class MenuWidget extends HookWidget {
  const MenuWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final filteredProducts = useState<List<ProductModel>>([]);
    final tabBarIndex = useState(0);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        filteredProducts.value = products
            .where((element) =>
                element.category?.documentId == categories.first.documentId)
            .toList();
      });

      return () {};
    }, []);

    final playStoreLink = currentVendor?.playStoreLink;
    final appStoreLink = currentVendor?.appStoreLink;
 
 
    return Scaffold(
      backgroundColor: Colors.white,
      floatingActionButton: FloatingActionButton(
        backgroundColor: Colors.white,
        child: const Icon(
          Icons.language,
          color: Colors.black,
        ),
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => const ChangeLanguageDialog(),
          );
        },
      ),
      body: ListView(
        children: [
          const MenuAppBarWidget(),   
         
          CategoriesTabBarWidget(
            tabBarIndex: tabBarIndex,
            categories: categories,
            onTap: (index) {
              final selectedCategory = categories[index];
              tabBarIndex.value = index;

              filteredProducts.value = products
                  .where((element) =>
                      element.category?.documentId ==
                      selectedCategory.documentId)
                  .toList();
            },
          ),

          AppGaps.gap12,

          // * Products List
          ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) => ProductCardWidget(
                    product: filteredProducts.value[index],
                  ),
              separatorBuilder: (context, index) => Column(
                    children: [
                      AppGaps.gap4,
                      Divider(
                        color: Colors.grey.withOpacity(.3),
                      ),
                      AppGaps.gap4,
                    ],
                  ),
              itemCount: filteredProducts.value.length),
        ],
      ),
    );
  }
}


