import '../../../core/consts/api_strings.dart';
import '../../../core/shared/network/models/base_models/base_media.model.dart';

class CategoryModel {
  final int? id;
  final String? documentId;
  final String? englishName;
  final String? arabicName;
  final BaseMediaModel? featureImage;
  final String? createdAt;

  CategoryModel(
      {this.id,
      required this.englishName,
      this.arabicName,
      this.documentId,
      this.featureImage,
      this.createdAt});

  //! from Json
  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    if (json.isEmpty) return CategoryModel.empty();

    return CategoryModel(
      id: json[ApiStrings.id],
      englishName: json[ApiStrings.name],
      arabicName: json[ApiStrings.arabicName],
      documentId: json[ApiStrings.documentId],
      featureImage: json[ApiStrings.featureImage] != null
          ? BaseMediaModel.fromJson(json[ApiStrings.featureImage])
          : null,
      createdAt: json[ApiStrings.createdAt],
    );
  }

  //! from empty
  factory CategoryModel.empty() {
    return CategoryModel(
        englishName: null,
        arabicName: null,
        documentId: '',
        featureImage: BaseMediaModel.empty());
  }
}
