import '../../../core/consts/api_strings.dart';
import '../../../core/shared/network/models/base_models/base_media.model.dart';
import 'categories_model.dart';
import 'extra_setting_model.dart';

List<ProductModel> responseToProductModel(List<Never> response) {
  final allProducts = <ProductModel>[];

  for (var product in response) {
    allProducts.add(ProductModel.fromJson(product));
  }

  return allProducts;
}

class ProductModel {
  final int? id;
  final String? documentId;
  final String? englishTitle;
  final String? arabicTitle;
  final String? englishDescription;
  final String? arabicDescription;
  final num? totalPrice;
  final BaseMediaModel? thumbnail;
  final List<BaseMediaModel>? images;
  final num? salePrice;
  final bool isSale;
  final bool isActive;
  final CategoryModel? category;
  final List<ExtraSettingsModel> sizes;
  final List<ExtraSettingsModel> colors;

  ProductModel({
    this.id,
    this.englishDescription,
    this.arabicDescription,
    this.documentId,
    this.englishTitle,
    this.arabicTitle,
    this.category,
    this.totalPrice,
    this.thumbnail,
    this.images,
    this.salePrice,
    this.sizes = const [],
    this.colors = const [],
    this.isActive = false,
    this.isSale = false,
  });

  num get actualPrice => isSale ? (salePrice ?? 0) : (totalPrice ?? 0);

  //! json constructor---------------------------------------
  factory ProductModel.fromJson(Map<String, dynamic> json) {
    final images = json[ApiStrings.images] ?? [];

    final productCategory = json[ApiStrings.categories] != null
        ? (json[ApiStrings.categories] as List?)
        : [];

    final category = CategoryModel.fromJson(productCategory?.firstOrNull ?? {});

    final imagesList = List<BaseMediaModel>.from(
        images.map((image) => BaseMediaModel.fromJson(image)));
    //
    final thumbnail = imagesList.isEmpty ? null : imagesList.first;
    //
    // final colors = json[ApiStrings.colors] ?? [];
    //
    // final colorsList = List<ExtraSettingsModel>.from(
    //     colors.map((e) => ExtraSettingsModel.fromProductJson(e))).toList();
    //
    final sizes = json[ApiStrings.sizes] ?? [];
    //
    final sizesList = List<ExtraSettingsModel>.from(
        sizes.map((e) => ExtraSettingsModel.fromProductJson(e))).toList();
    //
    // final fromLocalThumbnail = json[ApiStrings.thumbnail] != null
    //     ? BaseMediaModel.fromJson(json[ApiStrings.thumbnail])
    //     : null;

    return ProductModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      englishTitle: json[ApiStrings.title],
      arabicTitle: json[ApiStrings.arabicTitle],
      totalPrice: json[ApiStrings.price] ?? 0,
      thumbnail: thumbnail,
      salePrice: json[ApiStrings.salePrice] ?? 0,
      isSale: json[ApiStrings.isSale] ?? false,
      images: imagesList,
      category: category,
      englishDescription: json[ApiStrings.description],
      arabicDescription: json[ApiStrings.arabicDescription],
      // isActive: json[ApiStrings.isActive] ?? false,
      // colors: colorsList,
      sizes: sizesList,
      // category: category,
    );
  }
}
