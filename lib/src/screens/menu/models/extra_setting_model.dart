import 'dart:developer';

import '../../../core/consts/api_strings.dart';

class MainExtraSettingsModel {
  final int? id;
  final String? documentId;
  final List<ExtraSettingsModel> sizes;
  final List<ExtraSettingsModel>? colors;
  final int bannerLimit;
  final bool isActiveWorkingTime;
  final num? minimumOrderCost;

  const MainExtraSettingsModel({
    this.id,
    this.documentId,
    this.bannerLimit = 0,
    this.sizes = const [],
    this.colors = const [],
    this.isActiveWorkingTime = true,
    this.minimumOrderCost,
  });

  factory MainExtraSettingsModel.fromJson(json) {
    if (json == null || json.isEmpty) {
      return const MainExtraSettingsModel();
    }

    final sizes = json[ApiStrings.sizes] as List;

    final sizesList = sizes.map((e) => ExtraSettingsModel.fromJson(e)).toList();

    // * ===========================================
    final colors = json[ApiStrings.colors] as List;

    final colorsList =
        colors.map((e) => ExtraSettingsModel.fromJson(e)).toList();

    return MainExtraSettingsModel(
        id: json[ApiStrings.id],
        documentId: json[ApiStrings.documentId],
        minimumOrderCost: json[ApiStrings.minimumOrderCost] ?? 0,
        isActiveWorkingTime: json[ApiStrings.isActiveWorkingTime] ?? true,
        sizes: sizesList,
        colors: colorsList,
        bannerLimit: json[ApiStrings.bannerLimit] ?? 0);
  }
}

class ExtraSettingsModel {
  final int? id;
  final String? englishName;
  final String? arabicName;
  num? price;

  ExtraSettingsModel({
    this.id,
    this.englishName = '',
    this.arabicName = '',
    this.price,
  });

  factory ExtraSettingsModel.fromJson(Map<String, dynamic> json) {
    log('ExtraSettingsModel.fromJson: $json');

    return ExtraSettingsModel(
      id: json[ApiStrings.id],
      englishName: json[ApiStrings.name] ?? '',
      arabicName: json[ApiStrings.arabicName] ?? '',
      price: json[ApiStrings.price],
    );
  }

  factory ExtraSettingsModel.fromProductJson(Map<String, dynamic> json) {
    return ExtraSettingsModel(
      englishName: json[ApiStrings.name] ?? '',
      price: json[ApiStrings.price],
    );
  }
}
