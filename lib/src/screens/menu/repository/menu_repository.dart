import 'dart:async';
import 'dart:io';

import 'package:idea2app_qr_landing/src/core/consts/network/api_endpoints.dart';
import 'package:idea2app_qr_landing/src/screens/menu/models/categories_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/products_model.dart';

class MenuRepository {
  Future<List<CategoryModel>> getCategoriesMenuData(
      {bool withPopulate = true}) async {
    try {
      final NetworkApiServices networkApiService = NetworkApiServices();
      final response =
          await networkApiService.getResponse(ApiEndpoints.categoriesByVendor);

      final categoriesList = (response['data'] as List)
          .map((e) => CategoryModel.fromJson(e))
          .toList();

      return categoriesList;
    } catch (e, s) {
      Log.e('getMenuData: ${e.toString()} $s');
      rethrow;
    }
  }

  //! Get products By Category ====================================
  Future<List<ProductModel>> getProductsByCategory() async {
    try {
      final NetworkApiServices networkApiService = NetworkApiServices();

      final response =
          await networkApiService.getResponse(ApiEndpoints.productsByVendor);

      final products = (response['data'] as List)
          .map((e) => ProductModel.fromJson(e))
          .toList();

      return products;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }
}
