import 'package:idea2app_qr_landing/src/core/consts/app_constants.dart';

String filteredPhone(
  String phone, {
  bool withOutCountryCode = false,
  removeCountryCode = false,
}) {
  final countryCode = withOutCountryCode || removeCountryCode ? '' : '+972';
  final phoneWithoutCountryCode = removeCountryCode
      ? (phone.startsWith('+972') ? phone.substring(4) : phone)
      : phone;

  if (removeCountryCode) return '0$phoneWithoutCountryCode';

  return '$countryCode${phone.startsWith('0') ? phone.substring(1) : phoneWithoutCountryCode}';
}

class ApiEndpoints {
  static const String url = 'https://backend.idea2app.tech';
  static const String baseUrl = '$url/api';

  //? Auth
  static const String register = "$baseUrl/auth/local/register";
  static const String login = "$baseUrl/auth/local";

  static const populate = 'pLevel&sort[0]=createdAt:desc';

  static String baseUrlWithPopulate(String url) {
    final mark = url.contains('?') ? '&' : '?';

    return '$baseUrl/$url$mark$populate';
  }

  //? APIs
  static final String vendorByBusinessName = baseUrlWithPopulate(
    'vendors?filters[business_name]=${AppConsts.vendorBusinessName}',
  );

  static final categoriesByVendor = baseUrlWithPopulate(
      'product-categories?filters[vendor][business_name]=${AppConsts.vendorBusinessName}');

  static final productsByVendor = baseUrlWithPopulate(
      'products?filters[vendor][business_name]=${AppConsts.vendorBusinessName}');
}
