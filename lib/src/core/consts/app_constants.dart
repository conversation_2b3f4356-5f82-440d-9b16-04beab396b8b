import 'package:flutter/cupertino.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../generated/l10n.dart';

class AppConsts {
  static String vendorBusinessName = 'chicken-bliss'; 
  static String appName = 'Idea2App QR Landing'; 
  static const Locale locale = Locale('en');

  static const List<Locale> supportedLocales = [
    locale, 
    Locale('ar'),
  ];

  static String langCodeToString(BuildContext context,
      {required String langCode}) {
    switch (langCode) {
      case 'en':
        return context.tr.english;
      case 'ar':
        return context.tr.arabic;
      default:
        return context.tr.english;
    }
  }

  static bool get isEnglish =>
      GetStorageService.getData(key: LocalKeys.language) == 'en';

  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];
}
