import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_qr_landing/src/core/consts/local/local_keys.dart';

import '../../consts/local/get_storage.dart';

class SettingsLocalRepo {
  Locale currentLocal() {
    final langCode = GetStorageHandler.getLocalData(key: LocalKeys.language);

    if (langCode != null) {
      return Locale(langCode);
    } else {
      if (kIsWeb) {
        return const Locale('en');
      }

      String languageCode = Platform.localeName.split('_')[0];

      return Locale(languageCode);
    }
  }

  Future<void> updateLanguage(Locale locale) async {
    GetStorageHandler.setLocalData(
        key: LocalKeys.language, value: locale.languageCode);
  }
}
