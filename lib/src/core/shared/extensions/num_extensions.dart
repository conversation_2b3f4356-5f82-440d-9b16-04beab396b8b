import 'package:flutter/cupertino.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';

extension NumExtensions on num {
  // // currency
  // String get toCurrency {
  //  return this.isEmpty ? '' : '${this} NIS';
  // }

  String toCurrency(BuildContext context) {
    final currency = context.isEnglish ? 'EGP' : 'جنيه';

    final price = context.isEnglish
        ? '$currency${toString()}'
        : '${toString()} $currency';
    return toString().isEmpty ? '' : price;
  }
}
