import 'package:flutter/material.dart';

import '../../../../generated/l10n.dart';

extension Localization on BuildContext {
  //? Localization shortcuts
  S get tr => S.of(this);
}

extension AppSettings on BuildContext {
  bool get isEnglish {
    final locale = Localizations.localeOf(this);
    return locale.languageCode == 'en';
  }
}

extension Display on BuildContext {
  bool get isDesktop {
    return MediaQuery.of(this).size.width > 800;
  }

  bool get isSmallDesktop {
    return MediaQuery.of(this).size.width > 600;
  }
}
