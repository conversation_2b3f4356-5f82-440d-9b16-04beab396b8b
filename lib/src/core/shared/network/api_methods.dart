import 'package:idea2app_qr_landing/src/core/consts/network/api_endpoints.dart';
import 'package:xr_helper/xr_helper.dart';

import 'models/vendor_models/vendor_model.dart';

Future<VendorModel?> getCurrentVendor() async {
  final networkApiService = NetworkApiServices();

  try {
    final url = ApiEndpoints.vendorByBusinessName;

    final response = await networkApiService.getResponse(url);

    final vendorList = response['data'] as List;

    final vendorData = vendorList.isNotEmpty ? vendorList.firstOrNull : {};

    return VendorModel.fromJson(vendorData);
  } catch (e) {
    Log.e('getCurrentVendorError: ${e.toString()}');
    rethrow;
  }
}
