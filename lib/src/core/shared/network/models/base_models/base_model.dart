import 'package:equatable/equatable.dart';

class BaseModel extends Equatable {
  final int id;
  final String documentId;
  final DateTime? createdAt;

  const BaseModel({
    this.id = 0,
    this.documentId = '',
    this.createdAt,
  });

  factory BaseModel.fromJson(Map<String, dynamic> json) {
    return BaseModel(
      id: json['id'] ?? 0,
      documentId: json['documentId'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  List<Object?> get props => [id, documentId, createdAt];
}
