import 'dart:developer';

import '../../../../consts/api_strings.dart';
import '../base_models/base_media.model.dart';
import 'config_model.dart';

enum VendorType {
  free,
  deleted,
  monthly,
  quarter,
  semester,
  annually,
}

class VendorModel {
  final int? id;
  final String? fcmToken;
  final String? documentId;
  final String? name;
  final String? email;
  final bool? isActive;
  final String? address;
  final String? businessName;
  final String? phone;
  final BaseMediaModel? logo;
  final List<CurrencyModel>? currencies;
  final VendorType? vendorType;
  final String? businessType;
  final ConfigModel? config;
  final AboutVendorModel? aboutVendor;
  final QrLandingModel? qrLanding;
  final String? playStoreLink;
  final String? appStoreLink;

  const VendorModel({
    this.id,
    this.logo,
    this.fcmToken,
    this.businessName,
    this.documentId,
    this.vendorType,
    this.name,
    this.email,
    this.isActive = true,
    this.address,
    this.phone,
    this.currencies,
    this.businessType,
    this.aboutVendor,
    this.config,
    this.qrLanding,
    this.playStoreLink,
    this.appStoreLink,
  });

  String? get logoUrl => logo?.url;

  bool? get isMenuQRType => qrLanding?.qrType == QRType.menu;

  //? Business types
  bool get isClothes =>
      businessType?.toLowerCase() == 'clothes' ||
      businessType?.toLowerCase() == 'others';

  bool get isMarket => businessType?.toLowerCase() == 'market';

  bool get isAccessories => businessType?.toLowerCase() == 'accessories';

  bool get isRestaurant => businessType?.toLowerCase() == 'restaurant';

  bool get isFree => vendorType == VendorType.free;

  static VendorType getVendorType(String type) {
    switch (type) {
      case 'free':
        return VendorType.free;
      case 'deleted':
        return VendorType.deleted;
      case 'monthly':
        return VendorType.monthly;
      case 'quarter':
        return VendorType.quarter;
      case 'semester':
        return VendorType.semester;
      case 'annually':
        return VendorType.annually;
      default:
        return VendorType.free;
    }
  }

  factory VendorModel.fromJson(Map<String, dynamic> json) {
    return VendorModel(
      id: json['id'],
      documentId: json['documentId'],
      logo: json['logo'] != null ? BaseMediaModel.fromJson(json['logo']) : null,
      name: json['name'] ?? '',
      businessName: json['business_name'] ?? 'default',
      email: json['email'] ?? '',
      isActive: json['active'] ?? true,
      vendorType: getVendorType(json['type'] ?? 'free'),
      address: json['address'] ?? '',
      phone: json['phone'] ?? '',
      fcmToken: json['device_token'],
      playStoreLink: json[ApiStrings.playStoreLink],
      appStoreLink: json[ApiStrings.appStoreLink],
      businessType:
          json['business_type']?.toString().toLowerCase() ?? 'default',
      currencies: json['currencies'] != null && json['currencies'] is! List<int>
          ? List<CurrencyModel>.from(
              json['currencies'].map(
                (x) => CurrencyModel.fromJson(x),
              ),
            )
          : [],
      aboutVendor: json['about'] != null
          ? AboutVendorModel.fromJson(json['about'])
          : null,
      config:
          json['config'] != null ? ConfigModel.fromJson(json['config']) : null,
      qrLanding: json['qr_landing'] != null
          ? QrLandingModel.fromJson(json['qr_landing'])
          : null,
    );
  }

  @override
  String toString() {
    return 'VendorModel{id: $id, documentId: $documentId name: $name, email: $email, isActive: $isActive, address: $address, phone: $phone, logo: ${logo?.url}, currencies: $currencies,  vendorType: $vendorType, businessType: $businessType, businessName: $businessName}';
  }
}

class ConfigModel {
  final bool showMap;
  final bool showBoundaries;
  final bool isActiveWorkingTime;
  final num minimumOrderCost;
  final String? primaryColor;
  final String? defaultLanguage;

  const ConfigModel({
    this.showMap = false,
    this.showBoundaries = false,
    this.isActiveWorkingTime = true,
    this.minimumOrderCost = 0,
    this.primaryColor,
    this.defaultLanguage,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) {
    return ConfigModel(
      showMap: json['show_map'] ?? false,
      showBoundaries: json['show_boundaries'] ?? false,
      isActiveWorkingTime: json['is_active_working_time'] ?? true,
      defaultLanguage: json['default_language'] ?? 'en',
      minimumOrderCost: json['minimum_order_cost'] ?? 0,
      primaryColor: json['primary_color'],
    );
  }
}

enum QRType {
  social,
  menu,
}

class AboutVendorModel {
  final String privacy;
  final String about;
  final String whatsapp;
  final String facebook;
  final String instagram;
  final String tiktok;
  final String youtube;

  const AboutVendorModel({
    this.privacy = '',
    this.about = '',
    this.whatsapp = '',
    this.facebook = '',
    this.instagram = '',
    this.tiktok = '',
    this.youtube = '',
  });

  factory AboutVendorModel.fromJson(Map<String, dynamic> json) {
    return AboutVendorModel(
      privacy: json['privacy'] ?? '',
      about: json['about'] ?? '',
      whatsapp: json['whatsapp'] ?? '',
      facebook: json['facebook'] ?? '',
      instagram: json['instagram'] ?? '',
      tiktok: json['tiktok'] ?? '',
      youtube: json['youtube'] ?? '',
    );
  }
}

class QrLandingModel {
  final String qrTitle;
  final String qrDescription;
  final QRType qrType;

  const QrLandingModel({
    this.qrTitle = '',
    this.qrDescription = '',
    this.qrType = QRType.social,
  });

  factory QrLandingModel.fromJson(Map<String, dynamic> json) {
    log('asfsafafffsf ${json['type']}');

    return QrLandingModel(
      qrTitle: json['title'] ?? '',
      qrDescription: json['description'] ?? '',
      qrType: json['type'] != null
          ? (json['type'] == 'menu' ? QRType.menu : QRType.social)
          : QRType.social,
    );
  }
}
