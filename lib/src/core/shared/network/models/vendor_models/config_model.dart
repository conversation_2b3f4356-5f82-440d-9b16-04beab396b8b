class ConfigModel {
  final bool showMap;
  final bool showBoundaries;
  final bool isActiveWorkingTime;
  final num minimumOrderCost;
  final String? primaryColor;

  const ConfigModel({
    this.showMap = false,
    this.showBoundaries = false,
    this.isActiveWorkingTime = true,
    this.minimumOrderCost = 0,
    this.primaryColor,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) {
    return ConfigModel(
      showMap: json['show_map'] ?? false,
      showBoundaries: json['show_boundaries'] ?? false,
      isActiveWorkingTime: json['is_active_working_time'] ?? true,
      minimumOrderCost: json['minimum_order_cost'] ?? 0,
      primaryColor: json['primary_color'],
    );
  }
}

class CurrencyModel {
  final String? currencyEn;
  final String? currencyAr;
  final String? symbol;

  CurrencyModel({
    this.currencyEn,
    this.currencyAr,
    this.symbol,
  });

  factory CurrencyModel.fromJson(Map<String, dynamic> json) {
    final symbol = json['symbol'];

    return CurrencyModel(
      currencyEn:
          symbol == null || symbol.isEmpty ? json['currency_en'] : symbol,
      currencyAr:
          symbol == null || symbol.isEmpty ? json['currency_ar'] : symbol,
      symbol: symbol,
    );
  }

  Map<String, dynamic> toJson() => {
        'currency_en': currencyEn,
        'currency_ar': currencyAr,
        'symbol': symbol,
      };
}
