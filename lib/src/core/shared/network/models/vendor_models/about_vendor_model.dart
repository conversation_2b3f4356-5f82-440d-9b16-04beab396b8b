enum QRType {
  social,
  menu,
}

class AboutVendorModel {
  final String privacy;
  final String about;
  final String whatsapp;
  final String facebook;
  final String instagram;
  final String tiktok;
  final String youtube;

  const AboutVendorModel({
    this.privacy = '',
    this.about = '',
    this.whatsapp = '',
    this.facebook = '',
    this.instagram = '',
    this.tiktok = '',
    this.youtube = '',
  });

  factory AboutVendorModel.fromJson(Map<String, dynamic> json) {
    return AboutVendorModel(
      privacy: json['privacy'] ?? '',
      about: json['about'] ?? '',
      whatsapp: json['whatsapp'] ?? '',
      facebook: json['facebook'] ?? '',
      instagram: json['instagram'] ?? '',
      tiktok: json['tiktok'] ?? '',
      youtube: json['youtube'] ?? '',
    );
  }
}

class QrLandingModel {
  final String qrTitle;
  final String qrDescription;
  final QRType qrType;

  const QrLandingModel({
    this.qrTitle = '',
    this.qrDescription = '',
    this.qrType = QRType.social,
  });

  factory QrLandingModel.fromJson(Map<String, dynamic> json) {
    return QrLandingModel(
      qrTitle: json['title'] ?? '',
      qrDescription: json['description'] ?? '',
      qrType: json['type'] != null
          ? (json['type'] == 'menu' ? QRType.menu : QRType.social)
          : QRType.social,
    );
  }
}
