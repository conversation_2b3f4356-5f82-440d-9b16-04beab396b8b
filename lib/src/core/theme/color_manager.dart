import 'package:flutter/material.dart';

class ColorManager {
  static int primaryColorInt = 0xFF000000;
  static Color primaryColor = Color(primaryColorInt);
  static const lightPrimaryColor = Colors.white;
  static const blue = Color(0xFF0052AF);
  // const Color(0xFFEEBA2C).withOpacity(0.6);

  static const secondaryColor = Color(0xFFECF0FF);

  static const buttonColor = secondaryColor;
  static final containerColor = Colors.grey.withOpacity(0.1);
  static const backgroundColor = Color(0xFF161616);
  static const selectedContainerColor = Color(0xFFD3E1E2);
  static const fieldColor = Color(0xFFCBD5E1);
  static const white = Color(0xFFE1E2E0);
  static const lightWhite = Color(0xFFE1E2E0);
  static const black = Color(0xFF262626);
  static const grey = Color(0xFFf5f5f5);
  static const greyIcon = Color(0xFF9E9E9E);
  static const highlightColor = Color(0xFFFFFFFF);
  static const lightGrey = Color(0xFFEEF1F6);

  static const shimmerBaseColor = Color(0xFFCECECE);
  static const cardColor = black;
  static const darkGrey = Color(0xFFA4A4A4);
  static const darkBlue = Color(0xFF23292F);
  static const iconColor = Color(0xFF727272);
  static const errorColor = Color(0xFFE74C3C);
  static const successColor = Color(0xFF2ECC71);
}
