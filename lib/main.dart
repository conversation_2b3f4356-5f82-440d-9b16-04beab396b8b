import 'dart:html' as html;
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:idea2app_qr_landing/src/app.dart';
import 'package:idea2app_qr_landing/src/core/consts/app_constants.dart';
import 'package:idea2app_qr_landing/src/core/shared/network/api_methods.dart';
import 'package:idea2app_qr_landing/src/core/shared/network/models/vendor_models/vendor_model.dart';
import 'package:idea2app_qr_landing/src/core/shared/utils/change_web_data.dart';
import 'package:idea2app_qr_landing/src/core/theme/color_manager.dart';
import 'package:idea2app_qr_landing/src/screens/menu/models/categories_model.dart';
import 'package:idea2app_qr_landing/src/screens/menu/models/products_model.dart';
import 'package:idea2app_qr_landing/src/screens/menu/repository/menu_repository.dart';
import 'package:xr_helper/xr_helper.dart';

VendorModel? currentVendor;

List<CategoryModel> categories = [];
List<ProductModel> products = [];

//! git commit --amend --author="Idea2App <<EMAIL>>"
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_idea2app" git push --force qr_landing master

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorageService.init();

  HttpOverrides.global = MyHttpOverrides();

  final url = Uri.parse(html.window.location.href);

  if (url.pathSegments.isNotEmpty) {
    AppConsts.vendorBusinessName = url.pathSegments.last;
  }

  final menuRepository = MenuRepository();

  currentVendor = await getCurrentVendor();

  categories = await menuRepository.getCategoriesMenuData();

  products = await menuRepository.getProductsByCategory();

  Color hexToColor(String? hex, {Color fallback = Colors.white}) {
    if (hex == null) return fallback;

    hex = hex
        .replaceAll('#', '')
        .replaceAll(RegExp(r'[^A-Fa-f0-9]'), '')
        .trim()
        .toUpperCase();

    if (hex.length == 6) hex = 'FF$hex';
    if (hex.length != 8) return fallback;

    try {
      return Color(int.parse('0x$hex'));
    } catch (_) {
      return fallback;
    }
  }

  AppConsts.appName = currentVendor?.name ?? 'QR Landing Page';
  ColorManager.primaryColor = hexToColor(
    currentVendor?.config?.primaryColor,
    fallback: Color(ColorManager.primaryColorInt),
  );

  setWebFavIcon(currentVendor!.logoUrl!);

  runApp(const ProviderScope(child: BaseApp()));

  // Notify the web landing page that Flutter is ready
  WidgetsBinding.instance.addPostFrameCallback((_) {
    Future.delayed(const Duration(milliseconds: 500), () {
      html.window.dispatchEvent(html.CustomEvent('flutter-app-ready'));
    });
  });
}
