// Flutter bootstrap with optimizations

// Device capability detection for optimal performance
function getCanvasKitMaximumSurfaces() {
  const memory = navigator.deviceMemory || 4;
  const cpuCores = navigator.hardwareConcurrency || 2;

  if (memory <= 2 || cpuCores <= 2) {
    return 2; // Low-end device
  } else if (memory >= 8 && cpuCores >= 6) {
    return 8; // High-end device
  } else {
    return 4; // Medium-range device
  }
}

// Preload essential assets
function preloadEssentialAssets() {
  const preloadAssets = [
    '/flutter.js',
    '/main.dart.js',
    '/assets/fonts/Roboto-Regular.ttf'
  ];

  preloadAssets.forEach(asset => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = asset;
    link.as = asset.endsWith('.js') ? 'script' : 
               (asset.endsWith('.ttf') ? 'font' : 'fetch');
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
}

// Update loading status
function updateLoadingStatus(status) {
  const loadingElement = document.getElementById('loading-status');
  if (loadingElement) {
    loadingElement.innerHTML = status + '<span class="loading-dots"></span>';
  }
}

// Initialize Flutter with optimizations
function initializeFlutter() {
  preloadEssentialAssets();
  updateLoadingStatus("Setting up your digital experience");

  // Let Flutter handle its own initialization
  // We'll just monitor for when it's ready
  let checkCount = 0;
  const maxChecks = 60; // 30 seconds max

  const checkFlutterReady = setInterval(() => {
    checkCount++;

    // Multiple ways to detect Flutter is ready
    const flutterElements = [
      document.querySelector('flt-glass-pane'),
      document.querySelector('flutter-view'),
      document.querySelector('[flt-renderer]'),
      document.querySelector('flt-scene-host'),
      document.querySelector('canvas[flt-renderer]')
    ].filter(el => el !== null);

    // Check if Flutter has rendered content
    const hasContent = flutterElements.some(el =>
      el && (el.children.length > 0 || el.innerHTML.trim() !== '')
    );

    // Also check for Flutter's canvas elements
    const hasCanvas = document.querySelector('canvas[flt-renderer]') !== null;

    if (hasContent || hasCanvas || checkCount >= maxChecks) {
      clearInterval(checkFlutterReady);

      updateLoadingStatus("Ready to explore");
      console.log('Flutter elements detected, dispatching flutter-initialized event');
      window.dispatchEvent(new Event('flutter-initialized'));

      // Show Flutter content immediately
      flutterElements.forEach(el => {
        if (el) {
          el.style.opacity = '1';
          el.classList.add('flutter-ready');
        }
      });

      // If we hit max checks, force hide landing page
      if (checkCount >= maxChecks) {
        console.warn('Flutter detection timed out, forcing landing page hide');
        setTimeout(() => {
          window.dispatchEvent(new Event('flutter-app-ready'));
        }, 1000);
      }
    }
  }, 500);
}

function hideLandingPage() {
  const landingPage = document.getElementById('landing-page');
  if (landingPage) {
    landingPage.style.opacity = '0';
    landingPage.style.transition = 'opacity 0.5s ease-out';
    setTimeout(() => {
      landingPage.style.display = 'none';
    }, 500);
  }
}

// Start initialization when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeFlutter);
} else {
  initializeFlutter();
}
