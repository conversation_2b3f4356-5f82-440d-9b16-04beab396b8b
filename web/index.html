<!DOCTYPE html>
<html>
<head>
    <base href="$FLUTTER_BASE_HREF">
    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta name="description" content="QR Landing Page">

    <!-- iOS meta tags & icons -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="idea2app_qr_landing">
    <link rel="apple-touch-icon" href="icons/Icon-192.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png"/>

    <title>QR Landing</title>
    <link rel="manifest" href="manifest.json">

    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .mini-spinner {
            width: 40px;
            height: 40px;
            display: inline-block;
            position: relative;
        }

        .mini-spinner div {
            transform-origin: 20px 20px;
            animation: spin 1.2s linear infinite;
        }

        .mini-spinner div:after {
            content: " ";
            display: block;
            position: absolute;
            top: 2px;
            left: 18px;
            width: 4px;
            height: 10px;
            border-radius: 20%;
            background: #999;
        }

        .mini-spinner div:nth-child(1) { transform: rotate(0deg); animation-delay: -1.1s; }
        .mini-spinner div:nth-child(2) { transform: rotate(30deg); animation-delay: -1s; }
        .mini-spinner div:nth-child(3) { transform: rotate(60deg); animation-delay: -0.9s; }
        .mini-spinner div:nth-child(4) { transform: rotate(90deg); animation-delay: -0.8s; }
        .mini-spinner div:nth-child(5) { transform: rotate(120deg); animation-delay: -0.7s; }
        .mini-spinner div:nth-child(6) { transform: rotate(150deg); animation-delay: -0.6s; }
        .mini-spinner div:nth-child(7) { transform: rotate(180deg); animation-delay: -0.5s; }
        .mini-spinner div:nth-child(8) { transform: rotate(210deg); animation-delay: -0.4s; }
        .mini-spinner div:nth-child(9) { transform: rotate(240deg); animation-delay: -0.3s; }
        .mini-spinner div:nth-child(10) { transform: rotate(270deg); animation-delay: -0.2s; }
        .mini-spinner div:nth-child(11) { transform: rotate(300deg); animation-delay: -0.1s; }
        .mini-spinner div:nth-child(12) { transform: rotate(330deg); animation-delay: 0s; }

        @keyframes spin {
            0% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
<div class="mini-spinner">
    <div></div><div></div><div></div><div></div><div></div><div></div>
    <div></div><div></div><div></div><div></div><div></div><div></div>
</div>

<script src="flutter_bootstrap.js" async></script>
</body>
</html>
