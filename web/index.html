<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="description" content="Explore a wide range of content and enjoy a seamless browsing experience on our platform. Discover now!">
    <meta name="author" content="Content Explorer">
    <meta name="author" content="Idea2App_Customer">
    <meta name="keywords" content="Idea2App Customer">
    <meta property="og:title" content="Idea2App Customer">
    <meta property="og:description" content="Idea2App Customer - Explore Now!">
    <meta property="og:image" content="https://idea2app.com/wp-content/uploads/2021/06/idea2app-logo.png">
    <meta property="og:url" content="https://idea2app.com">
    <meta property="og:site_name" content="Idea2App Customer">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Idea2App Customer - Explore Now!">
    <meta name="twitter:description" content="Idea2App Customer - Explore Now!">
    <meta name="twitter:image" content="https://idea2app.com/wp-content/uploads/2021/06/idea2app-logo.png">
    <link rel="icon" type="image/png" sizes="32x32" href="./favicon.png">
    <link rel="manifest" href="./manifest.json">
    <meta name="theme-color" content="#ffffff">
    <title>Idea2App Customer</title>

    <!-- Preload critical resources for faster loading -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="landing-page.js" as="script">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Load optimized styles -->
    <link rel="stylesheet" href="styles.css">

    <!-- Firebase Configuration -->
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-analytics.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-messaging.js"></script>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCTUQH9tBTBxjAJSpDmDEVllVhWqmR0nR8"></script>

    <script>
        const firebaseConfig = {
            apiKey: "AIzaSyBynW2MzCY1fNEkpri4hiaXRqEU-j94OYk",
            authDomain: "idea2app-dev.firebaseapp.com",
            projectId: "idea2app-dev",
            storageBucket: "idea2app-dev.appspot.com",
            messagingSenderId: "163437334343",
            appId: "1:163437334343:web:8152fe124a60f0b7b98662",
            measurementId: "G-K3XP56P0NB"
        };
        firebase.initializeApp(firebaseConfig);
    </script>

    <!-- Inline critical CSS for immediate rendering -->
    <style>
        /* Minimal inline styles for immediate rendering */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', 'Arial', sans-serif;
            overflow: hidden;
        }

        /* Ensure Flutter content is hidden until ready */
        flt-glass-pane {
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }

        flt-glass-pane.flutter-ready {
            opacity: 1;
        }
    </style>
</head>
<body class="flutter-loading">
    <!-- Landing Page - Shows immediately while Flutter loads -->
    <div id="landing-page">
        <div class="landing-content">
            <img id="app-logo" class="app-logo" src="/icons/Icon-192.png" alt="App Logo">
            <h1 id="app-title" class="app-title">Idea2App</h1>
            <p id="app-subtitle" class="app-subtitle">Loading your digital experience...</p>
            <div id="loading-status" class="loading-status">Welcome to your digital experience<span class="loading-dots"></span></div>
            <!-- Circular loader removed for better performance -->
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>
    </div>

    <!-- Load landing page functionality -->
    <script src="landing-page.js"></script>

    <!-- Load Flutter bootstrap inline -->
    <script src="flutter_bootstrap.js"></script>

    <!-- Ensure Flutter loads -->
    <script>
        // Additional safety net to ensure Flutter loads
        window.addEventListener('load', function() {
            console.log('Window loaded, checking for Flutter...');

            // Give Flutter some time to initialize
            setTimeout(() => {
                // Check if Flutter script exists, if not create it
                if (!document.querySelector('script[src*="main.dart.js"]') &&
                    !document.querySelector('script[src*="flutter.js"]')) {
                    console.log('Creating Flutter script...');
                    const script = document.createElement('script');
                    script.src = 'main.dart.js';
                    script.type = 'application/javascript';
                    script.onload = () => console.log('Flutter script loaded');
                    script.onerror = () => console.error('Failed to load Flutter script');
                    document.body.appendChild(script);
                }
            }, 1000);
        });
    </script>

    <!-- Register service workers -->
    <script>
        if ('serviceWorker' in navigator) {
            // Register Flutter service worker for caching
            navigator.serviceWorker.register('/flutter_service_worker.js')
                .then(function(registration) {
                    console.log('Flutter Service Worker registered with scope:', registration.scope);
                })
                .catch(function(err) {
                    console.error('Flutter Service Worker registration failed:', err);
                });

            // Register Firebase messaging service worker
            navigator.serviceWorker.register('/firebase-messaging-sw.js')
                .then(function(registration) {
                    console.log('Firebase Service Worker registered with scope:', registration.scope);
                })
                .catch(function(err) {
                    console.error('Firebase Service Worker registration failed:', err);
                });
        }
    </script>
</body>
</html>
