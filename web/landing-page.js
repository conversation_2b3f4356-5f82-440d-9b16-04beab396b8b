// Landing page functionality
let flutterInitialized = false;
let loadingStartTime = Date.now();

// Initialize landing page
document.addEventListener('DOMContentLoaded', function() {
  initializeLandingPage();
  startLoadingAnimation();
});

function initializeLandingPage() {
  // Set app title and logo from URL or defaults
  const appTitle = document.getElementById('app-title');
  const appSubtitle = document.getElementById('app-subtitle');
  const appLogo = document.getElementById('app-logo');

  // Extract vendor name from URL path
  const pathSegments = window.location.pathname.split('/').filter(segment => segment);
  const vendorName = pathSegments.length > 0 ? pathSegments[0] : null;

  // Set dynamic content
  if (appTitle) {
    const titleText = vendorName ? vendorName.charAt(0).toUpperCase() + vendorName.slice(1) : 'Idea2App';
    appTitle.textContent = titleText;
  }

  if (appSubtitle) {
    appSubtitle.textContent = 'Your premium shopping destination';
  }

  // Handle logo display based on vendor name in path
  if (vendorName) {
    // Hide the existing image logo
    if (appLogo) {
      appLogo.style.display = 'none';
    }

    // Hide the normal title text to avoid duplication
    if (appTitle) {
      appTitle.style.display = 'none';
    }

    // Create animated text logo using vendor name from path
    createAnimatedTextLogo(vendorName);
  } else {
    // Set default logo - use favicon or default (for root path like localhost:55132/)
    if (appLogo) {
      appLogo.src = '/icons/Icon-192.png';
      appLogo.style.display = 'block';
    }

    // Show the normal title text
    if (appTitle) {
      appTitle.style.display = 'block';
    }
  }
//  if (appLogo) {
//    const favicon = document.querySelector('link[rel="icon"]');
//    if (favicon && favicon.href) {
//      appLogo.src = favicon.href;
//      appLogo.onerror = function() {
//        // Fallback to default icon
//        this.src = '/icons/Icon-512.png';
//      };
//    } else {
//      appLogo.src = '/icons/Icon-512.png';
//    }
//  }
}

function createAnimatedTextLogo(nameText) {
  const landingContent = document.querySelector('.landing-content');
  const appLogo = document.getElementById('app-logo');
  const subtitle = document.getElementById('app-subtitle');

  if (!landingContent || !appLogo) return;

  // Create animated text logo element
  const animatedLogo = document.createElement('div');
  animatedLogo.id = 'animated-text-logo';
  animatedLogo.className = 'animated-text-logo';
  animatedLogo.textContent = nameText;

  // Prefer to insert it before subtitle if exists, else before the original logo
  if (subtitle) {
    landingContent.insertBefore(animatedLogo, subtitle);
  } else {
    appLogo.parentNode.insertBefore(animatedLogo, appLogo);
  }
}
function startLoadingAnimation() {
    const loadingMessages = [
      'Welcome to your shopping destination',
      'Loading amazing products',
      'Preparing your personalized experience',
      'Almost ready to shop'
    ];

  let messageIndex = 0;
  const statusElement = document.getElementById('loading-status');

  // Update loading message every 2 seconds for faster feedback
  const messageInterval = setInterval(() => {
    if (flutterInitialized) {
      clearInterval(messageInterval);
      return;
    }

    messageIndex = (messageIndex + 1) % loadingMessages.length;
    if (statusElement) {
      statusElement.innerHTML = loadingMessages[messageIndex] + '<span class="loading-dots"></span>';
    }
  }, 2000);

  // Set initial message
  if (statusElement) {
    statusElement.innerHTML = loadingMessages[0] + '<span class="loading-dots"></span>';
  }
}

// Listen for Flutter initialization
window.addEventListener('flutter-initialized', function() {
  flutterInitialized = true;
  const loadingTime = Date.now() - loadingStartTime;

  console.log(`Flutter initialized in ${loadingTime}ms`);

  // Update status
  const statusElement = document.getElementById('loading-status');
  if (statusElement) {
    statusElement.innerHTML = 'Welcome to the Store<span class="loading-dots"></span>';
  }

  // Ensure all Flutter elements are visible
  const flutterElements = [
    'flt-glass-pane',
    'flutter-view',
    '[flt-renderer]',
    'flt-scene-host',
    'canvas[flt-renderer]'
  ];

  flutterElements.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
      element.style.opacity = '1';
      element.classList.add('flutter-ready');
    }
  });

  // Update body class
  document.body.classList.remove('flutter-loading');
  document.body.classList.add('flutter-ready');
});

// Listen for Flutter app ready event from Dart
window.addEventListener('flutter-app-ready', function() {
  console.log('Flutter app is fully ready');

  // Ensure transition happens smoothly
  setTimeout(() => {
    const landingPage = document.getElementById('landing-page');
    if (landingPage) {
      landingPage.style.opacity = '0';
      landingPage.style.transition = 'opacity 0.5s ease-out';
      setTimeout(() => {
        landingPage.style.display = 'none';
      }, 500);
    }
  }, 500);
});

// Handle errors
window.addEventListener('error', function(event) {
  console.error('Landing page error:', event.error);
  const statusElement = document.getElementById('loading-status');
  if (statusElement && !flutterInitialized) {
    statusElement.innerHTML = 'Loading failed. Please refresh the page.<span class="loading-dots"></span>';
    statusElement.style.color = '#ffcccb';
  }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
  console.error('Unhandled promise rejection:', event.reason);
});

// Prevent context menu on landing page
document.addEventListener('contextmenu', function(e) {
  if (!flutterInitialized) {
    e.preventDefault();
  }
});

// Add keyboard shortcuts for development
document.addEventListener('keydown', function(e) {
  // Ctrl+Shift+R for hard refresh during development
  if (e.ctrlKey && e.shiftKey && e.key === 'R') {
    location.reload(true);
  }

  // Ctrl+Shift+D for debug info
  if (e.ctrlKey && e.shiftKey && e.key === 'D') {
    console.log('Debug Info:', {
      flutterInitialized,
      loadingTime: Date.now() - loadingStartTime,
      userAgent: navigator.userAgent,
      memory: navigator.deviceMemory,
      cores: navigator.hardwareConcurrency,
      flutterElements: {
        glassPane: !!document.querySelector('flt-glass-pane'),
        flutterView: !!document.querySelector('flutter-view'),
        renderer: !!document.querySelector('[flt-renderer]'),
        canvas: !!document.querySelector('canvas[flt-renderer]')
      }
    });
  }

  // ESC key to force hide landing page (for debugging)
  if (e.key === 'Escape' && !flutterInitialized) {
    console.log('Force hiding landing page');
    const landingPage = document.getElementById('landing-page');
    if (landingPage) {
      landingPage.style.display = 'none';
    }
  }
});

// Performance monitoring
function logPerformanceMetrics() {
  if (window.performance && window.performance.timing) {
    const timing = window.performance.timing;
    const loadTime = timing.loadEventEnd - timing.navigationStart;
    const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;

    console.log('Performance Metrics:', {
      totalLoadTime: loadTime + 'ms',
      domReadyTime: domReady + 'ms',
      flutterInitTime: (Date.now() - loadingStartTime) + 'ms',
      vendorLoadTime: 'async'
    });
  }
}

// Log performance metrics when Flutter is ready
window.addEventListener('flutter-initialized', logPerformanceMetrics);

// Emergency fallback to hide landing page after 10 seconds
setTimeout(() => {
  if (!flutterInitialized) {
    console.warn('Flutter taking too long to load, forcing landing page hide');
    const landingPage = document.getElementById('landing-page');
    if (landingPage) {
      landingPage.style.opacity = '0';
      landingPage.style.transition = 'opacity 0.5s ease-out';
      setTimeout(() => {
        landingPage.style.display = 'none';
      }, 500);
    }
    // Also dispatch the event to ensure any listeners are notified
    window.dispatchEvent(new Event('flutter-app-ready'));
  }
}, 10000);

// Additional fallback - listen for any Flutter-related events
window.addEventListener('flutter-first-frame', function() {
  console.log('Flutter first frame detected');
  setTimeout(() => {
    window.dispatchEvent(new Event('flutter-app-ready'));
  }, 1000);
});

// Listen for DOM changes that might indicate Flutter is ready
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.addedNodes.length > 0) {
      const hasFlutterContent = Array.from(mutation.addedNodes).some(node => {
        return node.nodeType === Node.ELEMENT_NODE && (
          node.tagName === 'FLT-GLASS-PANE' ||
          node.tagName === 'FLUTTER-VIEW' ||
          node.querySelector && (
            node.querySelector('flt-glass-pane') ||
            node.querySelector('flutter-view') ||
            node.querySelector('canvas[flt-renderer]')
          )
        );
      });

      if (hasFlutterContent && !flutterInitialized) {
        console.log('Flutter content detected via DOM observer');
        setTimeout(() => {
          window.dispatchEvent(new Event('flutter-app-ready'));
        }, 2000);
      }
    }
  });
});

// Start observing
observer.observe(document.body, {
  childList: true,
  subtree: true
});
